/**
 * AnimaGen Conversion Routes
 * 
 * This module provides endpoints for converting master MP4 files to different formats.
 * It uses the ConversionService to perform fast format conversions.
 */

import express, { Request, Response } from 'express';
import path from 'path';
import fs from 'fs';
import { 
  RequestWithSessionId,
  ExportFormat
} from '../types';
import { 
  asyncHandler,
  createError
} from '../utils';
import ConversionService from '../services/ConversionService';
import config from '../config';

// Create router
const router = express.Router();

/**
 * Convert master MP4 to specified format
 * POST /convert/:format
 */
router.post('/:format', asyncHandler(async (req: RequestWithSessionId, res: Response) => {
  try {
    const { format } = req.params;
    const { 
      masterId,
      masterPath,
      quality = 3, // Default medium quality (1-5 scale)
      sessionId,
      customSettings = {}
    } = req.body;

    console.log('🔄 Format conversion requested');
    console.log(`📝 Conversion request: ${format}, quality: ${quality}`);

    // Validate format
    const validFormats = Object.values(ExportFormat).map(f => f.toLowerCase());
    if (!validFormats.includes(format.toLowerCase())) {
      throw createError(`Invalid format: ${format}. Valid formats: ${validFormats.join(', ')}`, 400);
    }

    const outputFormat = format.toLowerCase() as ExportFormat;

    // Determine master file path
    let resolvedMasterPath: string;
    
    if (masterPath) {
      // Use provided master path
      resolvedMasterPath = path.isAbsolute(masterPath) ? masterPath : path.join(config.app.outputDir, masterPath);
    } else if (masterId) {
      // Construct path from master ID
      resolvedMasterPath = path.join(config.app.outputDir, `${masterId}.mp4`);
    } else {
      throw createError('Either masterId or masterPath is required', 400);
    }

    console.log(`📁 Master path: ${resolvedMasterPath}`);

    // Validate master file exists
    if (!fs.existsSync(resolvedMasterPath)) {
      throw createError(`Master file not found: ${resolvedMasterPath}`, 404);
    }

    // Validate quality (1-5 scale)
    const qualityNum = parseInt(quality.toString());
    if (isNaN(qualityNum) || qualityNum < 1 || qualityNum > 5) {
      throw createError('Quality must be a number between 1 and 5', 400);
    }

    // Get conversion service
    const conversionService = ConversionService.getInstance();

    // Perform conversion
    console.log('🚀 Starting format conversion');
    const result = await conversionService.convertMaster({
      masterPath: resolvedMasterPath,
      outputFormat,
      quality: qualityNum,
      sessionId: sessionId || 'unknown',
      customSettings
    });

    console.log('✅ Conversion completed successfully');
    console.log(`🔗 Export URL: ${result.exportUrl}`);

    // Return success response
    res.json({
      success: true,
      exportId: result.exportId,
      exportUrl: result.exportUrl,
      format: result.format,
      quality: result.quality,
      fileSize: result.fileSize,
      duration: result.duration,
      message: result.message,
      settings: {
        format: result.format,
        quality: result.quality,
        fileSize: `${(result.fileSize / 1024 / 1024).toFixed(2)}MB`,
        duration: `${result.duration.toFixed(1)}s`
      }
    });

  } catch (error) {
    console.error('❌ Conversion failed:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    const statusCode = (error as any).statusCode || 500;
    
    res.status(statusCode).json({
      success: false,
      error: errorMessage,
      message: 'Format conversion failed'
    });
  }
}));

/**
 * Get available formats and quality options
 * GET /convert/formats
 */
router.get('/formats', asyncHandler(async (req: Request, res: Response) => {
  try {
    const formats = Object.values(ExportFormat).map(format => ({
      format: format.toLowerCase(),
      name: format,
      description: getFormatDescription(format),
      qualityLevels: [
        { level: 1, name: 'Low', description: 'Smallest file size' },
        { level: 2, name: 'Medium-Low', description: 'Good compression' },
        { level: 3, name: 'Medium', description: 'Balanced quality/size' },
        { level: 4, name: 'High', description: 'High quality' },
        { level: 5, name: 'Maximum', description: 'Best quality, largest size' }
      ]
    }));

    res.json({
      success: true,
      formats,
      message: 'Available formats and quality levels'
    });

  } catch (error) {
    console.error('❌ Failed to get formats:', error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to get available formats',
      message: 'Internal server error'
    });
  }
}));

/**
 * Get conversion status
 * GET /convert/status/:exportId
 */
router.get('/status/:exportId', asyncHandler(async (req: Request, res: Response) => {
  try {
    const { exportId } = req.params;
    
    if (!exportId) {
      throw createError('Export ID is required', 400);
    }

    // Check if export file exists (simple status check)
    const possibleFormats = Object.values(ExportFormat);
    let foundFile = null;
    let foundFormat = null;

    for (const format of possibleFormats) {
      const exportPath = path.join(config.app.outputDir, `${exportId}.${format.toLowerCase()}`);
      if (fs.existsSync(exportPath)) {
        foundFile = exportPath;
        foundFormat = format;
        break;
      }
    }

    if (foundFile) {
      const fileStats = fs.statSync(foundFile);
      
      res.json({
        success: true,
        exportId,
        status: 'completed',
        format: foundFormat,
        fileSize: fileStats.size,
        exportUrl: `/output/${path.basename(foundFile)}`,
        createdAt: fileStats.birthtime,
        message: 'Export completed'
      });
    } else {
      res.json({
        success: true,
        exportId,
        status: 'not_found',
        message: 'Export not found or still processing'
      });
    }

  } catch (error) {
    console.error('❌ Status check failed:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    const statusCode = (error as any).statusCode || 500;
    
    res.status(statusCode).json({
      success: false,
      error: errorMessage,
      message: 'Status check failed'
    });
  }
}));

/**
 * Delete export file
 * DELETE /convert/:exportId
 */
router.delete('/:exportId', asyncHandler(async (req: Request, res: Response) => {
  try {
    const { exportId } = req.params;
    
    if (!exportId) {
      throw createError('Export ID is required', 400);
    }

    // Find and delete export file
    const possibleFormats = Object.values(ExportFormat);
    let deletedFiles = 0;

    for (const format of possibleFormats) {
      const exportPath = path.join(config.app.outputDir, `${exportId}.${format.toLowerCase()}`);
      if (fs.existsSync(exportPath)) {
        fs.unlinkSync(exportPath);
        deletedFiles++;
        console.log(`🗑️ Export deleted: ${exportPath}`);
      }
    }

    if (deletedFiles > 0) {
      res.json({
        success: true,
        message: `${deletedFiles} export file(s) deleted successfully`
      });
    } else {
      res.json({
        success: true,
        message: 'No export files found to delete'
      });
    }

  } catch (error) {
    console.error('❌ Export deletion failed:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    const statusCode = (error as any).statusCode || 500;
    
    res.status(statusCode).json({
      success: false,
      error: errorMessage,
      message: 'Export deletion failed'
    });
  }
}));

/**
 * Get format description
 */
function getFormatDescription(format: ExportFormat): string {
  switch (format) {
    case ExportFormat.MP4:
      return 'High-quality video format, widely supported';
    case ExportFormat.GIF:
      return 'Animated image format, perfect for web sharing';
    case ExportFormat.WEBM:
      return 'Modern web video format, excellent compression';
    case ExportFormat.MOV:
      return 'Apple video format, high quality for professional use';
    default:
      return 'Video format';
  }
}

export default router;
